# 🚀 Build Connect - Quick Start Guide

## ⚡ One-Command Setup

```bash
cd mongodb-sample-data
npm install
npm run setup
```

**That's it!** Your database is ready with 203 realistic documents across 18 collections.

## 🔐 Login Credentials

**Universal Password**: `Build@123`

### Sample Accounts:
- **User**: raj<PERSON>.<EMAIL> / Build@123
- **Contractor**: <EMAIL> / Build@123  
- **Broker**: <EMAIL> / Build@123
- **Admin**: <EMAIL> / Build@123

## 📊 What You Get

✅ **203 Documents** across **18 Collections**  
✅ **Perfect Data Relationships** - All ObjectIds properly linked  
✅ **Standardized Passwords** - All users use `Build@123`  
✅ **Database Indexes** - Optimized for performance  
✅ **Realistic Indian Data** - Names, addresses, phone numbers  
✅ **Complete Workflows** - From user registration to project completion  

## 🗄️ Database Structure

| Collection | Records | Description |
|------------|---------|-------------|
| users | 12 | Platform users (10 contractors, 2 brokers) |
| contractors | 10 | Contractor profiles with portfolios |
| brokers | 2 | Broker profiles with service areas |
| sites | 10 | Property listings with geolocation |
| projects | 10 | Construction projects with progress |
| admins | 10 | Platform administrators |
| verificationrequests | 15 | Admin verification requests |
| servicerequests | 12 | Service requests between users |
| assets | 15 | File uploads (avatars, documents) |
| aadhaars | 12 | Aadhaar verification data |
| pans | 12 | PAN verification data |
| transactions | 12 | Payment transactions |
| notifications | 12 | User notifications |
| ratings | 12 | User ratings and reviews |
| customers | 12 | Support tickets |
| siteassets | 15 | Site images and documents |
| encumbrancecertificates | 10 | Property legal documents |
| propertytaxreceipts | 10 | Property tax records |

## 🛠️ Available Commands

```bash
npm run setup          # Complete setup (fix + insert + validate)
npm run install-data   # Insert data only
npm run validate       # Validate existing data
npm run clear-db       # Clear entire database
npm run fix-relationships  # Fix ObjectId relationships
```

## 🌍 Geographic Coverage

Sample data covers major Indian cities:
- Bangalore, Mumbai, Delhi, Hyderabad
- Chennai, Ahmedabad, Pune, Kochi
- Jaipur, Kolkata, Lucknow, Indore

## 🔧 Environment Variables

```bash
# Default
MONGODB_URL=mongodb://localhost:27017
DB_NAME=build_connect_db

# Custom
MONGODB_URL=************************:port DB_NAME=custom_db npm run setup
```

## ✅ Validation Results

```
📊 Collection Counts: ✅ PASS
🔐 Password Hashing: ✅ PASS  
🔗 Data Relationships: ✅ PASS
📊 Database Indexes: ✅ PASS
🎯 OVERALL STATUS: ✅ ALL VALIDATIONS PASSED
```

## 🎯 Ready for Development!

Your Build Connect database is now fully populated with:
- ✅ Proper user authentication (bcrypt hashed passwords)
- ✅ Complete project workflows (planning → in-progress → completed)
- ✅ Financial transactions and payment records
- ✅ User ratings, notifications, and support tickets
- ✅ Property documents and legal certificates
- ✅ Geospatial data for mapping features

**Start coding immediately!** All relationships are validated and working perfectly.
