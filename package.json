{"name": "backend", "version": "1.0.0", "type": "module", "private": true, "workspaces": ["user-management-service", "site-management-service", "rating-management-service", "payment-management-service", "notification-management-service", "customer-support-service", "admin-management-service", "api-gateway"], "main": ".eslintrc.js", "scripts": {"start": "concurrently \"npm --workspace user-management-service start\" \"npm --workspace site-management-service start\" \"npm --workspace rating-management-service start\" \"npm --workspace payment-management-service start\" \"npm --workspace notification-management-service start\" \"npm --workspace customer-support-service start\" \"npm --workspace admin-management-service start\" \"npm --workspace api-gateway start\"", "format": "prettier . --write", "lint": "eslint . --ext .js,.ts", "prettier:check": "prettier --check .", "lint:fix": "eslint . --ext .js,.ts --fix"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"concurrently": "^9.2.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-prettier": "^5.5.1", "nodemon": "^3.1.10"}}