# Build Connect Backend - MongoDB Sample Data

This directory contains comprehensive sample data for the Build Connect platform - a microservices-based backend for connecting property owners, brokers, and contractors for construction projects.

## 📊 Database Collections Overview

The platform consists of **18 MongoDB collections** with realistic sample data:

### Core Collections
1. **Users** (12 records) - Platform users (property owners, contractors, brokers)
2. **Contractors** (10 records) - Contractor profiles with portfolios and specialties
3. **Brokers** (12 records) - Broker profiles with service areas and ratings
4. **Sites** (10 records) - Property/site listings with geolocation data
5. **Projects** (10 records) - Construction projects with progress tracking
6. **Admins** (10 records) - Platform administrators

### Verification & Requests
7. **Verification Requests** (15 records) - Admin verification requests for users/sites
8. **Service Requests** (12 records) - Service requests between users

### User Data & Documents
9. **Assets** (15 records) - File uploads (avatars, portfolios, documents)
10. **Aadhaar** (12 records) - Aadhaar verification data
11. **PAN** (12 records) - PAN verification data

### Financial & Communication
12. **Transactions** (12 records) - Payment transactions and earnings
13. **Notifications** (12 records) - User notifications
14. **Ratings** (12 records) - User ratings and reviews
15. **Customer Support Tickets** (12 records) - Support tickets

### Site Documentation
16. **Site Assets** (15 records) - Site images and legal documents
17. **Encumbrance Certificates** (10 records) - Property legal documents
18. **Property Tax Receipts** (10 records) - Property tax payment records

## 🚀 Import Instructions

### Method 1: Using Node.js Script (Recommended) ⭐

The easiest way to import all data with proper password hashing:

```bash
# Navigate to the mongodb-sample-data directory
cd mongodb-sample-data

# Install dependencies
npm install

# Run the insertion script
npm run install-data

# Validate the data (optional)
node validate-data.js
```

**Features:**
- ✅ Automatically hashes all passwords to `Build@123`
- ✅ Creates proper database indexes
- ✅ Handles duplicate key errors gracefully
- ✅ Provides detailed progress and summary
- ✅ Validates data relationships

### Method 2: Using MongoDB Compass

1. **Open MongoDB Compass** and connect to your database
2. **Create Database**: `build_connect_db`
3. **For each collection**:
   - Click "Create Collection"
   - Use collection names from the table below
   - Click "ADD DATA" → "Import JSON or CSV file"
   - Select the corresponding JSON file
   - Click "Import"

### Method 3: Using MongoDB Shell (mongosh)

```bash
# Connect to MongoDB
mongosh "mongodb://localhost:27017/build_connect_db"

# Import each collection
mongoimport --db build_connect_db --collection users --file 01-users.json --jsonArray
mongoimport --db build_connect_db --collection contractors --file 02-contractors.json --jsonArray
mongoimport --db build_connect_db --collection brokers --file 03-brokers.json --jsonArray
mongoimport --db build_connect_db --collection sites --file 04-sites.json --jsonArray
mongoimport --db build_connect_db --collection projects --file 05-projects.json --jsonArray
mongoimport --db build_connect_db --collection admins --file 06-admins.json --jsonArray
mongoimport --db build_connect_db --collection verificationrequests --file 07-verification-requests.json --jsonArray
mongoimport --db build_connect_db --collection servicerequests --file 08-service-requests.json --jsonArray
mongoimport --db build_connect_db --collection assets --file 09-assets.json --jsonArray
mongoimport --db build_connect_db --collection aadhaars --file 10-aadhaar.json --jsonArray
mongoimport --db build_connect_db --collection pans --file 11-pan.json --jsonArray
mongoimport --db build_connect_db --collection transactions --file 12-transactions.json --jsonArray
mongoimport --db build_connect_db --collection notifications --file 13-notifications.json --jsonArray
mongoimport --db build_connect_db --collection ratings --file 14-ratings.json --jsonArray
mongoimport --db build_connect_db --collection customers --file 15-customer-support-tickets.json --jsonArray
mongoimport --db build_connect_db --collection assets --file 16-site-assets.json --jsonArray
mongoimport --db build_connect_db --collection encumbrancecertificates --file 17-encumbrance-certificates.json --jsonArray
mongoimport --db build_connect_db --collection propertytaxreceipts --file 18-property-tax-receipts.json --jsonArray
```

### Method 4: Manual mongoimport Commands

If you prefer to import manually:

## 📋 Collection Mapping

| File | Collection Name | Model File | Records |
|------|----------------|------------|---------|
| 01-users.json | users | user-management-service/model/user.js | 12 |
| 02-contractors.json | contractors | user-management-service/model/Contractor.js | 10 |
| 03-brokers.json | brokers | user-management-service/model/Broker.js | 12 |
| 04-sites.json | sites | site-management-service/model/site.js | 10 |
| 05-projects.json | projects | site-management-service/model/project.js | 10 |
| 06-admins.json | admins | admin-management-service/model/admin.js | 10 |
| 07-verification-requests.json | verificationrequests | admin-management-service/model/verificationRequests.js | 15 |
| 08-service-requests.json | servicerequests | site-management-service/model/serviceRequest.js | 12 |
| 09-assets.json | assets | user-management-service/model/Asset.js | 15 |
| 10-aadhaar.json | aadhaars | user-management-service/model/Aadhaar.js | 12 |
| 11-pan.json | pans | user-management-service/model/Pan.js | 12 |
| 12-transactions.json | transactions | payment-management-service/model/transaction.js | 12 |
| 13-notifications.json | notifications | notification-management-service/model/notification.js | 12 |
| 14-ratings.json | ratings | rating-management-service/model/rating.js | 12 |
| 15-customer-support-tickets.json | customers | customer-support-service/model/tickets.js | 12 |
| 16-site-assets.json | assets | site-management-service/model/Asset.js | 15 |
| 17-encumbrance-certificates.json | encumbrancecertificates | site-management-service/model/encumbranceCertificate.js | 10 |
| 18-property-tax-receipts.json | propertytaxreceipts | site-management-service/model/propertyTaxRecipt.js | 10 |

## 🔗 Data Relationships

The sample data includes proper relationships between collections:
- Users are linked to their Contractor/Broker profiles
- Sites are owned by Users and can have assigned Brokers/Contractors
- Projects link Users, Contractors, and Brokers
- Assets reference their parent entities (Users, Sites, etc.)
- Verification requests link to Users/Sites and Admins
- All timestamps and IDs are realistic and consistent

## 🌍 Geographic Coverage

Sample data covers major Indian cities:
- **Bangalore** (Karnataka)
- **Mumbai** (Maharashtra) 
- **Delhi** (NCR)
- **Hyderabad** (Telangana)
- **Chennai** (Tamil Nadu)
- **Ahmedabad** (Gujarat)
- **Pune** (Maharashtra)
- **Kochi** (Kerala)
- **Jaipur** (Rajasthan)
- **Kolkata** (West Bengal)
- **Lucknow** (Uttar Pradesh)
- **Indore** (Madhya Pradesh)

## 💡 Usage Tips

1. **User Roles**: Sample includes users with different roles (user, contractor, broker)
2. **Project States**: Projects in various states (Initiated, Planning, In Progress, Completed, Cancelled, On Hold)
3. **Verification Status**: Mixed verification statuses (pending, verified, rejected)
4. **Realistic Data**: All names, addresses, phone numbers, and other data are realistic but fictional
5. **Geolocation**: Sites include proper latitude/longitude coordinates for mapping features

## 🔧 Environment Setup

Make sure your MongoDB connection string in your services points to the correct database:
```javascript
const DATABASE_URL = "mongodb://localhost:27017/build_connect_db"
```

## 📞 Support

If you encounter any issues with the sample data import, please check:
1. MongoDB service is running
2. Database permissions are correct
3. JSON files are valid (use a JSON validator if needed)
4. Collection names match your model definitions

## 🛠️ Available Scripts

The package includes several utility scripts:

```bash
# Install all test data
npm run install-data

# Generate password hash for Build@123
npm run generate-hash

# Validate inserted data
node validate-data.js

# Clear entire database (use with caution!)
npm run clear-db
```

## 🔐 Login Credentials

**Password for ALL users**: `Build@123`

**Sample Login Accounts:**
- **User**: <EMAIL> / Build@123
- **Contractor**: <EMAIL> / Build@123
- **Broker**: <EMAIL> / Build@123
- **Admin**: <EMAIL> / Build@123

## 🎯 Environment Variables

You can customize the database connection:

```bash
# Default values
MONGODB_URL=mongodb://localhost:27017
DB_NAME=build_connect_db

# Custom example
MONGODB_URL=******************************************* DB_NAME=my_custom_db npm run install-data
```

**Total Records**: 218 documents across 18 collections
**Database Size**: Approximately 2-3 MB when imported
