#!/usr/bin/env node

/**
 * Build Connect - Database Cleanup Script
 * 
 * This script clears all collections in the Build Connect database
 * Use with caution - this will delete all data!
 * 
 * Usage: node clear-database.js
 */

const { MongoClient } = require('mongodb');
const readline = require('readline');

// Configuration
const config = {
    mongoUrl: process.env.MONGODB_URL || 'mongodb://localhost:27017',
    dbName: process.env.DB_NAME || 'build_connect_db'
};

const collections = [
    'users', 'contractors', 'brokers', 'sites', 'projects', 'admins',
    'verificationrequests', 'servicerequests', 'assets', 'aadhaars', 'pans',
    'transactions', 'notifications', 'ratings', 'customers', 'siteassets',
    'encumbrancecertificates', 'propertytaxreceipts'
];

class DatabaseCleaner {
    constructor() {
        this.client = null;
        this.db = null;
    }

    async connect() {
        try {
            console.log('🔌 Connecting to MongoDB...');
            this.client = new MongoClient(config.mongoUrl);
            await this.client.connect();
            this.db = this.client.db(config.dbName);
            console.log(`✅ Connected to database: ${config.dbName}`);
        } catch (error) {
            console.error('❌ MongoDB connection failed:', error.message);
            throw error;
        }
    }

    async clearAllCollections() {
        let totalDeleted = 0;
        const results = [];

        console.log('\n🗑️  Starting database cleanup...\n');

        for (const collectionName of collections) {
            try {
                const collection = this.db.collection(collectionName);
                const result = await collection.deleteMany({});
                
                console.log(`✅ Cleared ${result.deletedCount} documents from ${collectionName}`);
                totalDeleted += result.deletedCount;
                
                results.push({
                    collection: collectionName,
                    deleted: result.deletedCount
                });
                
            } catch (error) {
                console.error(`❌ Error clearing ${collectionName}:`, error.message);
                results.push({
                    collection: collectionName,
                    deleted: 0,
                    error: error.message
                });
            }
        }

        return { results, totalDeleted };
    }

    async printSummary(results, totalDeleted) {
        console.log('\n' + '='.repeat(50));
        console.log('🗑️  CLEANUP SUMMARY');
        console.log('='.repeat(50));
        
        results.forEach(result => {
            const status = result.error ? '❌' : '✅';
            const count = result.error ? 'FAILED' : result.deleted;
            console.log(`${status} ${result.collection.padEnd(25)} ${count.toString().padStart(8)}`);
        });
        
        console.log('='.repeat(50));
        console.log(`🎯 Total Documents Deleted: ${totalDeleted}`);
        console.log(`📁 Collections Processed: ${results.length}`);
        console.log(`✅ Successful: ${results.filter(r => !r.error).length}`);
        console.log(`❌ Failed: ${results.filter(r => r.error).length}`);
        console.log('='.repeat(50));
    }

    async close() {
        if (this.client) {
            await this.client.close();
            console.log('🔌 Database connection closed');
        }
    }
}

function askConfirmation() {
    return new Promise((resolve) => {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        console.log('⚠️  WARNING: This will delete ALL data from the database!');
        console.log(`Database: ${config.dbName}`);
        console.log(`MongoDB URL: ${config.mongoUrl}`);
        
        rl.question('\nAre you sure you want to continue? (yes/no): ', (answer) => {
            rl.close();
            resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
        });
    });
}

// Main execution function
async function main() {
    const cleaner = new DatabaseCleaner();
    
    try {
        // Ask for confirmation
        const confirmed = await askConfirmation();
        
        if (!confirmed) {
            console.log('❌ Operation cancelled by user');
            return;
        }

        // Connect to database
        await cleaner.connect();
        
        // Clear all collections
        const { results, totalDeleted } = await cleaner.clearAllCollections();
        
        // Print summary
        await cleaner.printSummary(results, totalDeleted);
        
        console.log('\n✅ Database cleanup completed successfully!');
        
    } catch (error) {
        console.error('💥 Script execution failed:', error.message);
        process.exit(1);
    } finally {
        await cleaner.close();
    }
}

// Handle script termination
process.on('SIGINT', async () => {
    console.log('\n⚠️  Script interrupted by user');
    process.exit(0);
});

// Run the script
if (require.main === module) {
    main();
}

module.exports = { DatabaseCleaner, config };
