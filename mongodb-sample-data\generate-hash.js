const bcrypt = require('bcrypt');

async function generateHash() {
    const password = 'Build@123';
    const saltRounds = 10;
    
    try {
        const hash = await bcrypt.hash(password, saltRounds);
        console.log('Password:', password);
        console.log('Hash:', hash);
        
        // Verify the hash works
        const isValid = await bcrypt.compare(password, hash);
        console.log('Verification:', isValid);
        
    } catch (error) {
        console.error('Error generating hash:', error);
    }
}

generateHash();
