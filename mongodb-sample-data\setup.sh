#!/bin/bash

# Build Connect - Quick Setup Script
# This script sets up the entire test database with one command

set -e  # Exit on any error

echo "🚀 Build Connect - Database Setup Script"
echo "========================================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are available"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Run the data insertion script
echo "🗄️  Inserting test data into MongoDB..."
node insert-test-data.js

if [ $? -ne 0 ]; then
    echo "❌ Failed to insert test data"
    exit 1
fi

echo "✅ Test data inserted successfully"

# Validate the data
echo "🔍 Validating inserted data..."
node validate-data.js

if [ $? -ne 0 ]; then
    echo "⚠️  Data validation failed, but data was inserted"
    echo "   You can still use the database, but some issues were detected"
else
    echo "✅ Data validation passed"
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Quick Start:"
echo "   Database: build_connect_db"
echo "   Password: Build@123 (for all users)"
echo "   Sample user: <EMAIL>"
echo "   Sample admin: <EMAIL>"
echo ""
echo "🔧 Available commands:"
echo "   npm run install-data  - Reinstall data"
echo "   npm run clear-db      - Clear database"
echo "   node validate-data.js - Validate data"
echo ""
