#!/usr/bin/env node

/**
 * Build Connect - MongoDB Test Data Insertion Script
 * 
 * This script inserts all sample data into MongoDB collections
 * Password for all users: Build@123
 * 
 * Usage: node insert-test-data.js
 */

const { MongoClient } = require('mongodb');
const bcrypt = require('bcrypt');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
    mongoUrl: process.env.MONGODB_URL || 'mongodb://localhost:27017',
    dbName: process.env.DB_NAME || 'build_connect_db',
    password: 'Build@123',
    saltRounds: 10
};

// Collection mapping with file names
const collections = [
    { name: 'users', file: '01-users.json' },
    { name: 'contractors', file: '02-contractors.json' },
    { name: 'brokers', file: '03-brokers.json' },
    { name: 'sites', file: '04-sites.json' },
    { name: 'projects', file: '05-projects.json' },
    { name: 'admins', file: '06-admins.json' },
    { name: 'verificationrequests', file: '07-verification-requests.json' },
    { name: 'servicerequests', file: '08-service-requests.json' },
    { name: 'assets', file: '09-assets.json' },
    { name: 'aadhaars', file: '10-aadhaar.json' },
    { name: 'pans', file: '11-pan.json' },
    { name: 'transactions', file: '12-transactions.json' },
    { name: 'notifications', file: '13-notifications.json' },
    { name: 'ratings', file: '14-ratings.json' },
    { name: 'customers', file: '15-customer-support-tickets.json' },
    { name: 'siteassets', file: '16-site-assets.json' },
    { name: 'encumbrancecertificates', file: '17-encumbrance-certificates.json' },
    { name: 'propertytaxreceipts', file: '18-property-tax-receipts.json' }
];

class DataInserter {
    constructor() {
        this.client = null;
        this.db = null;
        this.passwordHash = null;
    }

    async connect() {
        try {
            console.log('🔌 Connecting to MongoDB...');
            this.client = new MongoClient(config.mongoUrl);
            await this.client.connect();
            this.db = this.client.db(config.dbName);
            console.log(`✅ Connected to database: ${config.dbName}`);
        } catch (error) {
            console.error('❌ MongoDB connection failed:', error.message);
            throw error;
        }
    }

    async generatePasswordHash() {
        try {
            console.log('🔐 Generating password hash...');
            this.passwordHash = await bcrypt.hash(config.password, config.saltRounds);
            console.log(`✅ Password hash generated for: ${config.password}`);
        } catch (error) {
            console.error('❌ Password hash generation failed:', error.message);
            throw error;
        }
    }

    loadJsonFile(filename) {
        try {
            const filePath = path.join(__dirname, filename);
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error(`❌ Error loading ${filename}:`, error.message);
            throw error;
        }
    }

    updatePasswords(data, collectionName) {
        if (collectionName === 'users' || collectionName === 'admins') {
            return data.map(item => ({
                ...item,
                password: this.passwordHash
            }));
        }
        return data;
    }

    async clearCollection(collectionName) {
        try {
            const collection = this.db.collection(collectionName);
            const result = await collection.deleteMany({});
            console.log(`🗑️  Cleared ${result.deletedCount} documents from ${collectionName}`);
        } catch (error) {
            console.error(`❌ Error clearing ${collectionName}:`, error.message);
        }
    }

    async insertData(collectionName, data) {
        try {
            const collection = this.db.collection(collectionName);
            const result = await collection.insertMany(data, { ordered: false });
            console.log(`✅ Inserted ${result.insertedCount} documents into ${collectionName}`);
            return result.insertedCount;
        } catch (error) {
            if (error.code === 11000) {
                console.log(`⚠️  Some duplicate keys in ${collectionName}, continuing...`);
                return error.result?.insertedCount || 0;
            }
            console.error(`❌ Error inserting into ${collectionName}:`, error.message);
            throw error;
        }
    }

    async createIndexes() {
        try {
            console.log('📊 Creating database indexes...');
            
            // Users indexes
            await this.db.collection('users').createIndex({ email: 1 }, { unique: true });
            await this.db.collection('users').createIndex({ phone: 1 }, { unique: true });
            
            // Sites geospatial index
            await this.db.collection('sites').createIndex({ geo: '2dsphere' });
            await this.db.collection('sites').createIndex({
                name: 'text',
                addressLine1: 'text',
                location: 'text',
                state: 'text',
                district: 'text'
            });
            
            // Contractors and Brokers
            await this.db.collection('contractors').createIndex({ user: 1 }, { unique: true });
            await this.db.collection('brokers').createIndex({ user: 1 }, { unique: true });
            
            // Aadhaar and PAN unique indexes
            await this.db.collection('aadhaars').createIndex({ aadhaarNumber: 1 }, { unique: true });
            await this.db.collection('pans').createIndex({ panNumber: 1 }, { unique: true });
            
            console.log('✅ Database indexes created successfully');
        } catch (error) {
            console.error('❌ Error creating indexes:', error.message);
        }
    }

    async insertAllData() {
        let totalInserted = 0;
        const results = [];

        console.log('\n🚀 Starting data insertion...\n');

        for (const { name, file } of collections) {
            try {
                console.log(`📄 Processing ${file}...`);
                
                // Load data from JSON file
                const rawData = this.loadJsonFile(file);
                
                // Update passwords for user collections
                const data = this.updatePasswords(rawData, name);
                
                // Clear existing data
                await this.clearCollection(name);
                
                // Insert new data
                const insertedCount = await this.insertData(name, data);
                totalInserted += insertedCount;
                
                results.push({
                    collection: name,
                    file: file,
                    inserted: insertedCount,
                    total: data.length
                });
                
                console.log(''); // Empty line for readability
                
            } catch (error) {
                console.error(`❌ Failed to process ${file}:`, error.message);
                results.push({
                    collection: name,
                    file: file,
                    inserted: 0,
                    total: 0,
                    error: error.message
                });
            }
        }

        return { results, totalInserted };
    }

    async printSummary(results, totalInserted) {
        console.log('\n' + '='.repeat(60));
        console.log('📊 INSERTION SUMMARY');
        console.log('='.repeat(60));
        
        results.forEach(result => {
            const status = result.error ? '❌' : '✅';
            const ratio = result.error ? 'FAILED' : `${result.inserted}/${result.total}`;
            console.log(`${status} ${result.collection.padEnd(20)} ${ratio.padStart(10)} ${result.file}`);
        });
        
        console.log('='.repeat(60));
        console.log(`🎯 Total Documents Inserted: ${totalInserted}`);
        console.log(`📁 Total Collections: ${results.length}`);
        console.log(`✅ Successful: ${results.filter(r => !r.error).length}`);
        console.log(`❌ Failed: ${results.filter(r => r.error).length}`);
        console.log('='.repeat(60));
        
        // Print login credentials
        console.log('\n🔐 LOGIN CREDENTIALS');
        console.log('='.repeat(30));
        console.log('Password for all users: Build@123');
        console.log('Sample user emails:');
        console.log('  - <EMAIL> (user)');
        console.log('  - <EMAIL> (contractor)');
        console.log('  - <EMAIL> (broker)');
        console.log('  - <EMAIL> (admin)');
        console.log('='.repeat(30));
    }

    async close() {
        if (this.client) {
            await this.client.close();
            console.log('🔌 Database connection closed');
        }
    }
}

// Main execution function
async function main() {
    const inserter = new DataInserter();
    
    try {
        // Connect to database
        await inserter.connect();
        
        // Generate password hash
        await inserter.generatePasswordHash();
        
        // Insert all data
        const { results, totalInserted } = await inserter.insertAllData();
        
        // Create indexes
        await inserter.createIndexes();
        
        // Print summary
        await inserter.printSummary(results, totalInserted);
        
    } catch (error) {
        console.error('💥 Script execution failed:', error.message);
        process.exit(1);
    } finally {
        await inserter.close();
    }
}

// Handle script termination
process.on('SIGINT', async () => {
    console.log('\n⚠️  Script interrupted by user');
    process.exit(0);
});

process.on('unhandledRejection', (error) => {
    console.error('💥 Unhandled promise rejection:', error);
    process.exit(1);
});

// Run the script
if (require.main === module) {
    main();
}

module.exports = { DataInserter, config };
