#!/usr/bin/env node

/**
 * Build Connect - Relationship Fixer Script
 * 
 * This script fixes ObjectId relationships between collections
 * to ensure data integrity
 */

const fs = require('fs');
const path = require('path');

// Load all JSON files
function loadJsonFile(filename) {
    const filePath = path.join(__dirname, filename);
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
}

function saveJsonFile(filename, data) {
    const filePath = path.join(__dirname, filename);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
}

function fixRelationships() {
    console.log('🔧 Fixing data relationships...\n');

    // Load all data
    const users = loadJsonFile('01-users.json');
    const contractors = loadJsonFile('02-contractors.json');
    const brokers = loadJsonFile('03-brokers.json');
    const sites = loadJsonFile('04-sites.json');
    const projects = loadJsonFile('05-projects.json');
    const admins = loadJsonFile('06-admins.json');
    const verificationRequests = loadJsonFile('07-verification-requests.json');
    const serviceRequests = loadJsonFile('08-service-requests.json');
    const assets = loadJsonFile('09-assets.json');
    const aadhaar = loadJsonFile('10-aadhaar.json');
    const pan = loadJsonFile('11-pan.json');
    const transactions = loadJsonFile('12-transactions.json');
    const notifications = loadJsonFile('13-notifications.json');
    const ratings = loadJsonFile('14-ratings.json');
    const customers = loadJsonFile('15-customer-support-tickets.json');
    const siteAssets = loadJsonFile('16-site-assets.json');
    const encumbranceCerts = loadJsonFile('17-encumbrance-certificates.json');
    const propertyTaxReceipts = loadJsonFile('18-property-tax-receipts.json');

    // Create user ID mappings
    const userIds = users.map(u => u._id);
    const adminIds = admins.map(a => a._id);
    
    // Get users by role
    const contractorUsers = users.filter(u => u.role === 'contractor');
    const brokerUsers = users.filter(u => u.role === 'broker');
    const regularUsers = users.filter(u => u.role === 'user');

    console.log(`📊 Found ${users.length} users (${contractorUsers.length} contractors, ${brokerUsers.length} brokers, ${regularUsers.length} regular)`);

    // Ensure we have enough contractor and broker users
    // Update user roles to match contractor/broker counts
    contractors.forEach((contractor, index) => {
        if (index < users.length) {
            const user = users[index];
            user.role = 'contractor';
            user.partnershipRequest = 'Contractor';
            contractor.user = user._id;

            // Fix verifiedBy to reference admin
            if (contractor.verificationStatus === 'verified') {
                contractor.verifiedBy = adminIds[index % adminIds.length];
            }
        }
    });
    console.log('✅ Fixed contractor-user relationships');

    // Fix broker relationships - limit to available users
    const maxBrokers = Math.min(brokers.length, users.length - contractors.length);
    const validBrokers = brokers.slice(0, maxBrokers);

    validBrokers.forEach((broker, index) => {
        const userIndex = contractors.length + index;
        if (userIndex < users.length) {
            const user = users[userIndex];
            user.role = 'broker';
            user.partnershipRequest = 'Broker';
            broker.user = user._id;

            // Fix verifiedBy to reference admin
            if (broker.verificationStatus === 'verified') {
                broker.verifiedBy = adminIds[index % adminIds.length];
            }
        }
    });

    // Update brokers array to only include valid ones
    brokers.length = 0;
    brokers.push(...validBrokers);

    console.log(`✅ Fixed broker-user relationships (${validBrokers.length} brokers)`);

    // Fix site relationships
    sites.forEach((site, index) => {
        site.userId = userIds[index % userIds.length];
        
        // Assign brokers and contractors
        if (site.brokerId) {
            const brokerIndex = index % brokers.length;
            site.brokerId = brokers[brokerIndex]._id;
        }
        if (site.contractorId) {
            const contractorIndex = index % contractors.length;
            site.contractorId = contractors[contractorIndex]._id;
        }
        
        // Fix verifiedBy
        if (site.verifiedBy) {
            site.verifiedBy = adminIds[index % adminIds.length];
        }
    });
    console.log('✅ Fixed site relationships');

    // Fix project relationships
    projects.forEach((project, index) => {
        project.userId = userIds[index % userIds.length];
        
        if (project.contractorId) {
            const contractorIndex = index % contractors.length;
            project.contractorId = contractors[contractorIndex]._id;
        }
        if (project.brokerId) {
            const brokerIndex = index % brokers.length;
            project.brokerId = brokers[brokerIndex]._id;
        }
        
        // Fix progress logs
        project.progressLogs.forEach(log => {
            if (log.addedByRole === 'Contractor' && project.contractorId) {
                log.addedById = project.contractorId;
            } else if (log.addedByRole === 'Broker' && project.brokerId) {
                log.addedById = project.brokerId;
            }
        });
    });
    console.log('✅ Fixed project relationships');

    // Fix verification requests
    verificationRequests.forEach((request, index) => {
        if (request.type === 'site') {
            request.requesterId = sites[index % sites.length]._id;
        } else if (request.type === 'contractor') {
            request.requesterId = contractorUsers[index % contractorUsers.length]._id;
        } else if (request.type === 'broker') {
            request.requesterId = brokerUsers[index % brokerUsers.length]._id;
        }
        
        if (request.varifiedBy) {
            request.varifiedBy = adminIds[index % adminIds.length];
        }
    });
    console.log('✅ Fixed verification request relationships');

    // Fix service requests
    serviceRequests.forEach((request, index) => {
        request.userId = userIds[index % userIds.length];
        request.recipientId = contractors[index % contractors.length]._id;
        request.projectId = projects[index % projects.length]._id;
    });
    console.log('✅ Fixed service request relationships');

    // Fix assets
    assets.forEach((asset, index) => {
        if (asset.entityType === 'User') {
            asset.entityId = userIds[index % userIds.length];
        } else if (asset.entityType === 'ContractorProfile') {
            asset.entityId = contractors[index % contractors.length]._id;
        } else if (asset.entityType === 'BrokerProfile') {
            asset.entityId = brokers[index % brokers.length]._id;
        } else if (asset.entityType === 'Aadhaar') {
            asset.entityId = aadhaar[index % aadhaar.length]._id;
        } else if (asset.entityType === 'PAN') {
            asset.entityId = pan[index % pan.length]._id;
        }
    });
    console.log('✅ Fixed asset relationships');

    // Fix Aadhaar relationships
    aadhaar.forEach((record, index) => {
        record.userId = userIds[index % userIds.length];
    });
    console.log('✅ Fixed Aadhaar relationships');

    // Fix PAN relationships
    pan.forEach((record, index) => {
        record.userId = userIds[index % userIds.length];
    });
    console.log('✅ Fixed PAN relationships');

    // Fix transaction relationships
    transactions.forEach((transaction, index) => {
        transaction.user_id = userIds[index % userIds.length];
    });
    console.log('✅ Fixed transaction relationships');

    // Fix notification relationships
    notifications.forEach((notification, index) => {
        notification.user_id = userIds[index % userIds.length];
    });
    console.log('✅ Fixed notification relationships');

    // Fix rating relationships
    ratings.forEach((rating, index) => {
        rating.user_id = userIds[index % userIds.length];
        
        if (rating.ratedUserType === 'contractor') {
            rating.ratedUserId = contractors[index % contractors.length].user;
        } else if (rating.ratedUserType === 'broker') {
            rating.ratedUserId = brokers[index % brokers.length].user;
        }
        
        rating.projectId = projects[index % projects.length]._id;
    });
    console.log('✅ Fixed rating relationships');

    // Fix customer support relationships
    customers.forEach((ticket, index) => {
        ticket.user_id = userIds[index % userIds.length];
        if (ticket.assignedTo) {
            ticket.assignedTo = adminIds[index % adminIds.length];
        }
    });
    console.log('✅ Fixed customer support relationships');

    // Fix site assets
    siteAssets.forEach((asset, index) => {
        asset.entityId = sites[index % sites.length]._id;
    });
    console.log('✅ Fixed site asset relationships');

    // Fix encumbrance certificates
    encumbranceCerts.forEach((cert, index) => {
        cert.entityId = sites[index % sites.length]._id;
    });
    console.log('✅ Fixed encumbrance certificate relationships');

    // Fix property tax receipts
    propertyTaxReceipts.forEach((receipt, index) => {
        receipt.entityId = sites[index % sites.length]._id;
    });
    console.log('✅ Fixed property tax receipt relationships');

    // Save all fixed data
    console.log('\n💾 Saving fixed data...');

    saveJsonFile('01-users.json', users); // Save updated user roles
    saveJsonFile('02-contractors.json', contractors);
    saveJsonFile('03-brokers.json', brokers);
    saveJsonFile('04-sites.json', sites);
    saveJsonFile('05-projects.json', projects);
    saveJsonFile('07-verification-requests.json', verificationRequests);
    saveJsonFile('08-service-requests.json', serviceRequests);
    saveJsonFile('09-assets.json', assets);
    saveJsonFile('10-aadhaar.json', aadhaar);
    saveJsonFile('11-pan.json', pan);
    saveJsonFile('12-transactions.json', transactions);
    saveJsonFile('13-notifications.json', notifications);
    saveJsonFile('14-ratings.json', ratings);
    saveJsonFile('15-customer-support-tickets.json', customers);
    saveJsonFile('16-site-assets.json', siteAssets);
    saveJsonFile('17-encumbrance-certificates.json', encumbranceCerts);
    saveJsonFile('18-property-tax-receipts.json', propertyTaxReceipts);

    console.log('✅ All relationships fixed and saved!');
    console.log('\n🎉 You can now run the insertion script again.');
}

// Run the fix
if (require.main === module) {
    fixRelationships();
}

module.exports = { fixRelationships };
