@echo off
REM Build Connect - Quick Setup Script for Windows
REM This script sets up the entire test database with one command

echo 🚀 Build Connect - Database Setup Script
echo ========================================

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo ✅ Node.js and npm are available

REM Install dependencies
echo 📦 Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

REM Run the data insertion script
echo 🗄️  Inserting test data into MongoDB...
node insert-test-data.js
if %errorlevel% neq 0 (
    echo ❌ Failed to insert test data
    pause
    exit /b 1
)

echo ✅ Test data inserted successfully

REM Validate the data
echo 🔍 Validating inserted data...
node validate-data.js
if %errorlevel% neq 0 (
    echo ⚠️  Data validation failed, but data was inserted
    echo    You can still use the database, but some issues were detected
) else (
    echo ✅ Data validation passed
)

echo.
echo 🎉 Setup completed successfully!
echo.
echo 📋 Quick Start:
echo    Database: build_connect_db
echo    Password: Build@123 (for all users)
echo    Sample user: <EMAIL>
echo    Sample admin: <EMAIL>
echo.
echo 🔧 Available commands:
echo    npm run install-data  - Reinstall data
echo    npm run clear-db      - Clear database
echo    node validate-data.js - Validate data
echo.
pause
